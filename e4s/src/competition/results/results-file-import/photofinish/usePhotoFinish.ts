import * as PhotoFinishService from "./photo-finish-service";
import { computed, reactive } from "vue";
import { IBuilderCompetition } from "../../../../builder/builder-models";
import {
  getSocketInstance,
  getSocketState,
  SocketState,
} from "../../../../socket/socket-controller";
import { IBase, IsoDateTime } from "../../../../common/common-models";
import * as ResultsImportService from "../results-import-service";
import { ILogging } from "../../../../logging/useLogging";
import {
  getPhotoFinishData,
  setPhotoFinishResponse,
} from "./photo-finish-data";
import { ResultsFileImportSubmitStatus } from "../useResultsFileImport";
import { convertToIsoDateTimeWithOffset } from "../../../../common/common-service-utils";
import { messageDispatchHelper } from "../../../../user-message/user-message-store";
import { USER_MESSAGE_LEVEL } from "../../../../user-message/user-message-models";
import { useLoggingV2 } from "../../../../logging/useLoggingV2";
import { format } from "date-fns";
import {
  ICommonFile,
  useCommonFiles,
} from "../../../../common/files/useCommonFiles";

export interface IPhotoFinishSocketMessageRaw {
  data: string;
  type: "message";
}

export interface IPhotoFinishSocketMessage<Data> {
  action: "pf_file";
  comp: IBase;
  domain: string;
  payload: Data;
  securityKey: string;
}

export interface IPhotoFinishSocketMessagePayload {
  data: {
    file: string; //  actually fileName, e.g. lynx.evt
    system: string | number;
  };
}

export interface IPhotoFinishPayload {
  file: string;
  data: string;
}

export interface IPhotoFinishControllerInput {
  domain: string;
  builderCompetition: IBuilderCompetition;
  system: string | number;
}

export interface IPhotoFinishControllerState {
  isLoading: boolean;
  builderCompetition: IBuilderCompetition;
  domain: string;
  system: string | number;
  targetDirectoryName: string;
  targetDirectoryHandle: any;
  backupsDirectoryName: string;
  backupsDirectoryHandle: any;
  latestPhotoFinishPayload: IPhotoFinishPayload;
  manualPhotoFinishPayload: IPhotoFinishPayload;
  commonFiles: ICommonFile[];
}

export interface IPhotoFinishLoggingMessage extends ILogging {
  status: ResultsFileImportSubmitStatus;
  serverMessage: string;
  isoDateTime: IsoDateTime;
  fileName: string;
  targetFileName: string;
}

export function factoryPhotoFinishController() {
  const state = PhotoFinishService.factoryPhotoFinishControllerState();

  const logging = useLoggingV2(false, false);
  logging.setMaxSize(100);

  const commonFiles = useCommonFiles();

  //  This a is to get round TS stuff... we need to update TS, etc....for another day.
  const windowInternal: any = window;

  function init(photoFinishControllerInput: IPhotoFinishControllerInput) {
    state.builderCompetition = photoFinishControllerInput.builderCompetition;
    state.domain = photoFinishControllerInput.domain;
  }

  async function ensureBackupsDirectoryExists(): Promise<void> {
    if (!state.targetDirectoryHandle) {
      throw new Error("Target directory not selected");
    }
    
    if (!state.backupsDirectoryHandle) {
      state.backupsDirectoryHandle = await state.targetDirectoryHandle.getDirectoryHandle(
        state.backupsDirectoryName,
        { create: true }
      );
    }
  }

  function pickTargetDirectory(): Promise<void> {
    return windowInternal.showDirectoryPicker().then((res: any) => {
      state.targetDirectoryHandle = res;
      state.targetDirectoryName = res.name;
      state.backupsDirectoryName = "backups";
      state.backupsDirectoryHandle = null; // Will be created when needed
      console.log("pickTargetDirectory", res);
      return createTestFile().then(loadFilesCurrentlyInDirectory);
    });
  }

  function createTestFile(): Promise<void> {
    return movePhotoFinishPayloadToTarget(
      {
        file: "e4s-start-file.txt",
        data:
          "Photo finish start file, created at: " +
          convertToIsoDateTimeWithOffset(new Date()),
      },
      "e4s-start-file.txt"
    );
  }

  function cancelManualFileSubmit() {
    state.manualPhotoFinishPayload = {
      file: "",
      data: "",
    };
  }

  function manualFileSubmit(): Promise<unknown> {
    state.isLoading = true;

    const originalFileName = state.manualPhotoFinishPayload.file;
    const newFileName =
      ResultsImportService.getProcessedFileName(originalFileName);

    return movePhotoFinishPayloadToTarget(
      state.manualPhotoFinishPayload,
      newFileName
    )
      .then(() => {
        const photoFinishLoggingMessage: IPhotoFinishLoggingMessage = {
          id: new Date().getTime(),
          fileName: originalFileName,
          message: originalFileName,
          status: "SUCCESS",
          serverMessage: "",
          isoDateTime: convertToIsoDateTimeWithOffset(new Date()),
          targetFileName: newFileName,
        };
        logging.addMessage(photoFinishLoggingMessage);

        state.manualPhotoFinishPayload = {
          file: "",
          data: "",
        };

        return setPhotoFinishResponse(
          state.builderCompetition.id,
          originalFileName
        );
      })
      .finally(() => {
        state.isLoading = false;
        return loadFilesCurrentlyInDirectory();
      });
  }

  function getFile(dir: any, fileName: string): Promise<any> {
    return new Promise<any>((resolve) => {
      let entry: any = null;
      dir
        .getFileHandle(fileName, { create: false })
        .then((res: any) => {
          console.log("then");
          entry = res;
        })
        .catch(() => {
          console.log("catch");
        })
        .finally(() => {
          console.log("finally");
          resolve(entry);
        });
    });
  }

  async function movePhotoFinishPayloadToTarget(
    photoFinishPayload: IPhotoFinishPayload,
    newFileName: string
  ) {
    const fileHandleCurrent = await getFile(
      state.targetDirectoryHandle,
      photoFinishPayload.file
    );

    if (fileHandleCurrent) {
      console.log(
        "movePhotoFinishPayloadToTarget...found existing file...moving to backups directory"
      );
      
      // Ensure backups directory exists
      await ensureBackupsDirectoryExists();
      
      // Move existing file to backups directory with timestamped name
      await fileHandleCurrent.move(state.backupsDirectoryHandle, newFileName);
      console.log(
        "movePhotoFinishPayloadToTarget...moved existing file to backups/" + newFileName
      );
    }

    console.log(
      "movePhotoFinishPayloadToTarget...create new file called: " +
        photoFinishPayload.file
    );
    console.log(
      "movePhotoFinishPayloadToTarget...write to file:",
      photoFinishPayload.data
    );
    const fileHandle = await state.targetDirectoryHandle.getFileHandle(
      photoFinishPayload.file,
      { create: true }
    );

    const writable = await fileHandle.createWritable();
    // Write the contents of the file to the stream.
    console.log(
      "movePhotoFinishPayloadToTarget...write to file:",
      photoFinishPayload.data
    );
    await writable.write(photoFinishPayload.data);

    // Close the file and write the contents to disk.
    await writable.close();
  }

  function socketReceiveMessage(
    photoFinishSocketMessageRaw: IPhotoFinishSocketMessageRaw
  ) {
    const message: IPhotoFinishSocketMessage<IPhotoFinishSocketMessagePayload> =
      JSON.parse(photoFinishSocketMessageRaw.data);
    processReceiveMessage(message);
  }

  function processReceiveMessage(
    message: IPhotoFinishSocketMessage<IPhotoFinishSocketMessagePayload>
  ) {
    console.log(
      "factoryPhotoFinishController().socketReceiveMessage()",
      message
    );
    if (
      PhotoFinishService.isSocketMessageOk(message, {
        compId: state.builderCompetition.id,
        domain: state.domain,
        system: state.system,
      })
    ) {
      if (state.targetDirectoryName.length === 0) {
        messageDispatchHelper(
          "Socket Message received but no target directory specified.",
          USER_MESSAGE_LEVEL.INFO.toString()
        );
        return;
      }

      console.log(
        "factoryPhotoFinishController().socketReceiveMessage()...is ok"
      );

      state.isLoading = true;

      const newFileName = ResultsImportService.getProcessedFileName(
        message.payload.data.file
      );

      console.log(
        "factoryPhotoFinishController().socketReceiveMessage()...newFileName: " +
          newFileName
      );

      const photoFinishLoggingMessage: IPhotoFinishLoggingMessage = {
        id: new Date().getTime(),
        fileName: message.payload.data.file,
        message: message.payload.data.file,
        status: "SUCCESS",
        serverMessage: "",
        isoDateTime: convertToIsoDateTimeWithOffset(new Date()),
        targetFileName: newFileName,
      };

      console.log(
        "factoryPhotoFinishController().socketReceiveMessage()...photoFinishLoggingMessage",
        photoFinishLoggingMessage
      );

      console.log(
        "factoryPhotoFinishController().socketReceiveMessage()...getPhotoFinishData compId: " +
          state.builderCompetition.id +
          " fileName: " +
          message.payload.data.file
      );

      getPhotoFinishData(
        state.builderCompetition.id,
        message.payload.data.file,
        state.system
      )
        .then((resp) => {
          console.log(
            "factoryPhotoFinishController().socketReceiveMessage()...getPhotoFinishData...resp: ",
            resp
          );
          const payload: IPhotoFinishPayload = {
            file: message.payload.data.file,
            data: resp.data.body,
          };
          console.log(
            "factoryPhotoFinishController().socketReceiveMessage()...getPhotoFinishData...payload: ",
            payload
          );

          return movePhotoFinishPayloadToTarget(payload, newFileName).then(
            () => {
              console.log("TODO...socketReceiveMessage...log something.");
              return setPhotoFinishResponse(
                state.builderCompetition.id,
                message.payload.data.file
              );
            }
          );
        })
        .catch((err) => {
          photoFinishLoggingMessage.serverMessage = err.message;
          photoFinishLoggingMessage.status = "ERROR";
        })
        .finally(() => {
          state.isLoading = false;
          logging.addMessage(photoFinishLoggingMessage);
          return loadFilesCurrentlyInDirectory();
        });
    }
  }

  function loadFilesCurrentlyInDirectory(): Promise<void> {
    return commonFiles
      .getFilesFromDirectory(state.targetDirectoryHandle)
      .then((cFiles) => {
        state.commonFiles = cFiles;
      });
  }

  function manualTestRetrieve() {
    const photoFinishSocketMessage: IPhotoFinishSocketMessage<IPhotoFinishSocketMessagePayload> =
      {
        action: "pf_file",
        securityKey: "",
        comp: {
          id: state.builderCompetition.id,
        },
        domain: "dev.entry4sports.co.uk",
        payload: {
          data: {
            file: "lynx.evt",
            system: "1",
          },
        },
      };

    processReceiveMessage(photoFinishSocketMessage);
  }

  /**
   * Turn "2023-06-02T17:51:01+01:00" into "17:51:01"
   * @param iso
   */
  function loggingIsoToDisplay(iso: IsoDateTime): string {
    return iso.split("T")[1].slice(0, 8);
  }

  async function editCommonFile(commonFile: ICommonFile) {
    const entry = await state.targetDirectoryHandle.getFileHandle(
      commonFile.name,
      {
        create: false,
      }
    );

    if (entry) {
      const file = await entry.getFile();
      const contents = await file.text();
      state.manualPhotoFinishPayload.file = file.name;
      state.manualPhotoFinishPayload.data = contents;
      console.log("editCommonFile...contents : " + contents);
    }
  }

  async function editDocument(
    photoFinishLoggingMessage: IPhotoFinishLoggingMessage
  ) {
    let fileName = "";
    if (
      logging.state.events.length === 1 ||
      (logging.state.events.length > 1 &&
        logging.state.events[0].message === photoFinishLoggingMessage.id)
    ) {
      fileName = logging.state.events[0].message;
    } else {
      fileName = photoFinishLoggingMessage.targetFileName;
    }

    const entry = await state.targetDirectoryHandle.getFileHandle(fileName, {
      create: false,
    });

    if (entry) {
      const file = await entry.getFile();
      const contents = await file.text();
      state.manualPhotoFinishPayload.file =
        ResultsImportService.getOriginalFileName(file.name);
      state.manualPhotoFinishPayload.data = contents;
      console.log("editDocument...contents : " + contents);
    }
  }

  async function editCurrent() {
    //  The newest message will give us what the active file name is...
    const latestFile: IPhotoFinishLoggingMessage = logging.state.events[0];

    const entry = await state.targetDirectoryHandle.getFileHandle(
      latestFile.fileName,
      {
        create: false,
      }
    );

    if (entry) {
      const file = await entry.getFile();
      const contents = await file.text();
      state.manualPhotoFinishPayload.file =
        ResultsImportService.getOriginalFileName(file.name);
      state.manualPhotoFinishPayload.data = contents;
      console.log("editDocument...contents : " + contents);
    }
  }

  function getSimpleTime(date: Date | IsoDateTime): string {
    if (typeof date === "string") {
      return date.split("T")[1].slice(0, 8);
    }
    return date.toTimeString().split(" ")[0];
  }

  return {
    state,
    init,
    pickTargetDirectory,
    movePhotoFinishPayloadToTarget,
    manualFileSubmit,
    cancelManualFileSubmit,
    socketReceiveMessage,
    processReceiveMessage,
    manualTestRetrieve,
    logging,
    loggingIsoToDisplay,
    editDocument,
    editCurrent,
    editCommonFile,
    commonFiles,
    getSimpleTime,
  };
}

export interface ISocketConfig {
  socketAlivePollingTimer: number;
  SOCKET_ALIVE_POLL_EVERY_MS: number;
  isTryingReconnect: boolean;
  currentSocketState: SocketState;
}

export function usePhotoFinishController() {
  const controller = factoryPhotoFinishController();

  const state = reactive(controller.state);

  const socketConfig = reactive<ISocketConfig>({
    socketAlivePollingTimer: 0,
    SOCKET_ALIVE_POLL_EVERY_MS: 2000,
    isTryingReconnect: false,
    currentSocketState: "NA",
  });

  let socketInstance = getSocketInstance();
  socketInstance.e4sSocket.addEventListener(
    "message",
    controller.socketReceiveMessage
  );

  checkSocketConnected();

  function checkSocketConnected(): void {
    socketConfig.socketAlivePollingTimer = window.setTimeout(() => {
      // const currentStatus = this.status;
      if (socketInstance && socketInstance.e4sSocket) {
        const currentSocketState = socketInstance.e4sSocket.readyState;
        socketConfig.currentSocketState = getSocketState(currentSocketState);

        // console.log(
        //   "socketAlivePollingTimer: " +
        //     socketConfig.socketAlivePollingTimer +
        //     " " +
        //     new Date() +
        //     " CleoSocketWrapperController.checkSocketConnected()...currentStatus: " +
        //     currentSocketState +
        //     ", Init at: " +
        //     format(socketInstance.startedTime, "HH:mm:ss") +
        //     ", isTryingReconnect: " +
        //     socketConfig.isTryingReconnect
        // );

        if (currentSocketState === WebSocket.CLOSED) {
          console.log(
            "socketAlivePollingTimer: " +
              socketConfig.socketAlivePollingTimer +
              " " +
              new Date() +
              " CleoSocketWrapperController.checkSocketConnected()...currentStatus: " +
              currentSocketState +
              ", Init at: " +
              format(socketInstance.startedTime, "HH:mm:ss") +
              ", isTryingReconnect: " +
              socketConfig.isTryingReconnect
          );

          if (!socketConfig.isTryingReconnect) {
            socketConfig.isTryingReconnect = true;

            socketInstance = getSocketInstance();
            socketInstance.e4sSocket.addEventListener(
              "message",
              controller.socketReceiveMessage
            );
            socketConfig.isTryingReconnect = false;
            //  Do not get out of this "if" else will trigger another checkSocketConnected()
            // this.checkSocketConnected();
            // return;
          }
        }
      }

      checkSocketConnected();
    }, socketConfig.SOCKET_ALIVE_POLL_EVERY_MS);
  }

  const getLoggingEvents = computed(() => {
    return controller.logging.state;
  });

  function destroy() {
    clearTimeout(socketConfig.socketAlivePollingTimer);
    socketInstance.e4sSocket.removeEventListener(
      "message",
      controller.socketReceiveMessage
    );
  }

  return {
    controller,
    state,
    getLoggingEvents,
    socketConfig,
    destroy,
  };
}
