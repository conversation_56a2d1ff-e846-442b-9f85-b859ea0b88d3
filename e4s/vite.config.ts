import { defineConfig, loadEnv } from 'vite';
import vue2 from '@vitejs/plugin-vue2';
import legacy from '@vitejs/plugin-legacy';
import { visualizer } from 'rollup-plugin-visualizer';
import checker from 'vite-plugin-checker';
import path from 'path';

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isDev = command === 'serve';
  const disableDevLint = env.VITE_DISABLE_DEV_LINT === 'true';
  const devHost = env.VITE_E4S_HOST || 'https://dev.entry4sports.com';
  return {
    plugins: [
      vue2(),
      legacy({
        targets: ['defaults', 'not IE 11'],
        renderLegacyChunks: false,
        modernPolyfills: true,
      }),
      visualizer({
        filename: 'dist/vite-stats.html',
        open: false,
      }),
      ...(isDev && !disableDevLint
        ? [
            checker({
              // Disable TypeScript overlay checking for now to avoid @types/node vs TS version mismatch.
              // Keep ESLint running so we still get lint feedback during dev when not disabled via env.
              typescript: false,
              eslint: {
                lintCommand: 'eslint "./src/**/*.{ts,vue}"',
              },
            }),
          ]
        : []),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    server: {
      port: 5173,
      host: true,
      open: false,
      proxy: {
        '/resources': {
          target: devHost,
          changeOrigin: true,
          secure: false,
        },
      },
    },
    css: {
      devSourcemap: false,
    },
    build: {
      outDir: 'dist/vite',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            // Vendor libraries
            'vendor-vue': ['vue', 'vue-router', 'vuex'],
            'vendor-ui': ['vue-mq', 'vee-validate'],
            // 'vendor-date': ['vue-ctk-date-time-picker'], // REMOVED - replaced with native HTML5 inputs
            
            // Route chunks
            'routes-launch': ['./src/launch/launch-routes.ts'],
            'routes-launch-v2': ['./src/launch/v2/launch-routes-v2.ts'],
            'routes-scoreboard': ['./src/competition/scoreboard/scoreboard-routes.ts'],
            'routes-r4s': ['./src/competition/scoreboard/rs4/r4s-routes.ts'],
            
            // Store modules
            'store-core': ['./src/app.store.ts', './src/config/config-store.ts', './src/auth/auth-store.ts'],
            'store-modules': [
              './src/cart/cart-store.ts',
              './src/club/club-store.ts',
              './src/entry/entry-store.ts',
              './src/builder/builder-store.ts'
            ],
          }
        }
      }
    },
  };
});