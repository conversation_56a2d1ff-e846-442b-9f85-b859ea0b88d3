<template>
  <div>
    <div class="r4s-schedule-public--header-xx">
      <div class="row">
        <div class="col s12 m12 l12">
          <div class="e4s-flex-row">
            <div class="e4s-flex-column">
              <div
                v-if="!useSimpleHeader"
                class="r4s-schedule-public--comp-name"
                v-text="
                  '(' + r4sCompSchedule.compId + ') ' + r4sCompSchedule.name
                "
              ></div>

              <div
                :class="
                  useSimpleHeader
                    ? ''
                    : 'r4s-schedule-public--comp-date-section'
                "
              >
                <div
                  class="r4s-schedule-public--comp-date"
                  v-if="scheduleDates.length === 1 && !useSimpleHeader"
                >
                  <span v-text="getDate"></span>
                </div>

                <div v-if="scheduleDates.length > 1">
                  <span>Competition Dates:</span>
                  <span v-for="startDate in scheduleDates" :key="startDate.iso">
                    <a
                      class="r4s-schedule-public--comp-date-link"
                      :class="
                        isDateSelected(startDate)
                          ? 'r4s-schedule-public--comp-date-link-selected'
                          : ''
                      "
                      href="#"
                      v-on:click.prevent="selectedScheduleByDate(startDate)"
                    >
                      <span v-text="startDate.display"></span>
                    </a>
                  </span>
                </div>
              </div>
            </div>

            <div class="e4s-flex-row--end">
              <img
                v-if="!useSimpleHeader"
                :src="getLogoPath"
                class="r4s-schedule-public--logo"
              />
              <!--              <ButtonGenericBackV2-->
              <!--                v-if="useSimpleHeader && showSection !== sections.SCHEDULE"-->
              <!--              />-->
            </div>
          </div>
        </div>
      </div>

      <!--Feeding to Info-->
      <div v-if="getIsThisAFeederComp">
        <div class="e4s-section--padding"></div>

        <div class="row">
          <div class="col s12 m12 l12">
            Feeder competition to:
            <span
              v-text="
                '(' +
                r4sCompSchedule.autoEntries.selectedTargetComp.id +
                ') ' +
                r4sCompSchedule.autoEntries.selectedTargetComp.name
              "
            ></span>
          </div>
        </div>

        <div class="e4s-section--padding"></div>
      </div>
      <!--/Feeding to Info-->

      <!--Search Section-->
      <div
        class="row"
        v-if="[sections.ENTRIES, sections.RESULTS].indexOf(showSection) === -1"
      >
        <div class="col s12 m12 l12">
          <!--          <div class="e4s-flex-row e4s-gap&#45;&#45;standard">-->
          <div
            class="
              e4s-flex-row
              e4s-gap--standard
              e4s-full-width e4s-flex-center
            "
          >
            <FormGenericInputTemplateV2
              form-label="Quick Search"
              :show-label="false"
            >
              <div
                slot="field"
                class="e4s-flex-row e4s-gap--standard e4s-flex-center"
              >
                <InputDebounce
                  class="e4s-input"
                  id="quick-search"
                  :default-value="
                    scheduleCompPublicStoreState.defaultFilterValue
                  "
                  placeholder="Enter search..."
                  v-on:input="doFilter"
                />
                <a
                  href="#"
                  v-on:click.prevent="defaultFilterValue = ''"
                  v-if="defaultFilterValue.length > 0"
                >
                  Clear
                </a>
              </div>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2
              form-label="Event Type"
              :show-label="false"
            >
              <div
                slot="field"
                class="e4s-flex-row e4s-gap--standard e4s-flex-center"
              >
                <FieldSelectV2
                  :value="scheduleCompPublicStoreState.filterTrackField"
                  :data-array="filtersTrackFieldOptions"
                  v-on:input="filterTrackFieldSelected"
                />
              </div>
            </FormGenericInputTemplateV2>

            <FormGenericInputTemplateV2
              class="e4s-flex-row--end-xx"
              form-label="Admin Exports"
              :show-label="false"
              v-if="hasResultsPermissionForComp"
            >
              <div
                slot="field"
                class="e4s-flex-row e4s-gap--standard"
                style="margin: 4px 0"
                v-if="hasResultsPermissionForComp"
              >
                <ButtonGenericV2
                  text="Results List"
                  v-on:click="goToListFormat"
                />
                <ButtonGenericV2
                  text="List Format"
                  v-on:click="exportToPof10"
                  class="e4s-button--auto"
                >
                  <span
                    slot="button-content"
                    class="e4s-flex-row e4s-gap--small"
                  >
                    Power
                    <!--                    <img-->
                    <!--                      :src="require('../../../../images/po10transparent.png')"-->
                    <!--                      style="height: 18px"-->
                    <!--                      alt="Power of ten"-->
                    <!--                    />-->
                    <img
                      :src="
                        require('../../../../images/po10-white-transparent.gif')
                      "
                      style="height: 18px"
                      alt="Power of ten"
                    />
                    <!--                    <img-->
                    <!--                      class="e4s-icon"-->
                    <!--                      :src="require('../../../../images/po10.ico')"-->
                    <!--                    />-->
                  </span>
                </ButtonGenericV2>
                <ButtonGenericV2 text="Print QR" v-on:click="goToScheduleQr" />
              </div>
            </FormGenericInputTemplateV2>
          </div>
          <!--          </div>-->
        </div>
      </div>
    </div>
    <!--/Search Section-->

    <div v-show="showSection === sections.SCHEDULE">
      <div class="r4s-schedule-public--schedule-row-head">
        <div class="row">
          <div class="col s2 m2 l2">
            <span class="r4s-schedule-public--schedule-row-head-label"
              >Time</span
            >
          </div>
          <div class="col s8 m8 l8">
            <span class="r4s-schedule-public--schedule-row-head-label"
              >Event Name</span
            >
          </div>
          <div class="col s2 m2 l2">
            <div
              class="r4s-schedule-public--schedule-row-head-label center"
            ></div>
          </div>
        </div>
      </div>

      <!--      :class="-->
      <!--      index % 2 === 0 ? '' : 'r4s-schedule-public&#45;&#45;schedule-row-odd'-->
      <!--      "-->
      <div v-for="(scheduleTableRow, index) in scheduleTableRows">
        <div
          class="row r4s-schedule-public--schedule-row"
          :class="
            scheduleTableRow.resultsPossible
              ? ''
              : 'schedule-comp-public--row-cancel'
          "
        >
          <div class="col s2 m2 l2">
            <span
              :title="scheduleTableRow.startTime"
              v-text="getEventTimeForRow(scheduleTableRow)"
            ></span>
          </div>
          <div class="col s10 m10 l10">
            <div
              class="
                e4s-flex-row
                e4s-justify-flex-row-vert-center
                e4s-justify-flex-space-between
              "
            >
              <div class="e4s-flex-row e4s-gap--standard">
                <i
                  class="material-icons grey-text normal"
                  v-if="
                    (isAdmin || hasResultsPermissionForComp) &&
                    !scheduleTableRow.resultsPossible
                  "
                  >edit</i
                >

                <a
                  v-if="
                    (isAdmin || hasResultsPermissionForComp) &&
                    scheduleTableRow.resultsPossible
                  "
                  :href="
                    '#/results-entry/' +
                    r4sCompSchedule.compId +
                    '/' +
                    scheduleTableRow.eventGroupId
                  "
                >
                  <i class="material-icons red-text normal">edit</i>
                </a>

                <span v-text="scheduleTableRow.eventName"></span>

                <!--                <span-->
                <!--                  v-text="hasResultsWaitingText(scheduleTableRow)"-->
                <!--                  class="r4s-schedule-public&#45;&#45;pending-results"-->
                <!--                ></span>-->
              </div>

              <div
                class="e4s-flex-row e4s-gap--standard e4s-align-self-flex-end"
              >
                <div
                  v-if="
                    !scheduleTableRow.hasResults &&
                    getHasBuilderPermissionForComp
                  "
                  class="e4s-flex-row e4s-gap--standard"
                >
                  <ButtonGenericV2
                    v-if="
                      !getNoResultsConfirmationFor[
                        scheduleTableRow.eventGroupId
                      ]
                    "
                    :button-type="
                      scheduleTableRow.resultsPossible
                        ? 'secondary'
                        : 'secondary'
                    "
                    class="
                      e4s-button--slim
                      schedule-comp-public--slim-row-button
                    "
                    :text="
                      scheduleTableRow.resultsPossible ? 'Cancel' : 'Enable'
                    "
                    v-on:click="
                      doGetNoResultsConfirmation(scheduleTableRow, true)
                    "
                  />

                  <div
                    v-if="
                      getNoResultsConfirmationFor[scheduleTableRow.eventGroupId]
                    "
                  >
                    <ButtonGenericV2
                      class="
                        e4s-button--slim
                        schedule-comp-public--slim-row-button
                      "
                      text="Confirm"
                      v-on:click="
                        doSetNoResults(
                          scheduleTableRow,
                          !scheduleTableRow.resultsPossible
                        )
                      "
                    />
                    <ButtonGenericV2
                      class="
                        e4s-button--slim
                        schedule-comp-public--slim-row-button
                      "
                      text="Cancel"
                      button-type="tertiary"
                      v-on:click="
                        doGetNoResultsConfirmation(scheduleTableRow, false)
                      "
                    />
                  </div>
                </div>

                <ButtonGenericV2
                  v-if="getHasCompAnyResults"
                  button-type="secondary"
                  :disabled="!scheduleTableRow.hasResults"
                  class="e4s-button--slim schedule-comp-public--slim-row-button"
                  text="Results"
                  v-on:click="showResults(scheduleTableRow.eventGroupId)"
                />

                <ButtonGenericV2
                  button-type="secondary"
                  :disabled="scheduleTableRow.entries === 0"
                  class="e4s-button--slim schedule-comp-public--slim-row-button"
                  :text="scheduleTableRow.entries + ' Entries'"
                  v-on:click="showEntries(scheduleTableRow.eventGroupId)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--Entries-->
    <div v-if="showSection === sections.ENTRIES">
      <!--      <div class="e4s-section&#45;&#45;padding"></div>-->

      <ResultEntries
        :r4s-comp-schedule="r4sCompSchedule"
        :r4s-comp-schedule-target="r4sCompScheduleTarget"
        :schedule-table-row="scheduleTableRowDisplay"
        :has-results-permission-for-comp="hasResultsPermissionForComp"
        v-on:close="showSection = sections.SCHEDULE"
        @reloadSchedule="reloadSchedule"
      />
    </div>
    <!--/Entries-->

    <!--Results-->
    <div v-if="showSection === sections.RESULTS">
      <div class="e4s-section--padding" v-if="!useSimpleHeader"></div>

      <ResultReadWrapper
        :event-group-id="eventGroupIdSelected"
        :r4s-comp-schedule="r4sCompSchedule"
        :is-embedded="useSimpleHeader"
        v-on:gotoNextResult="gotoNextResult"
        v-on:showSchedule="showSection = sections.SCHEDULE"
      />
    </div>
    <!--/Results-->

    <div v-if="showSection === sections.RANKING">
      <div
        class="row"
        v-if="isLoadingLatestScore || showTheseEntries.length === 0"
      >
        <div class="col s12 m12 l12">
          <div class="left">
            <div v-if="isLoadingLatestScore">
              <LoadingSpinner></LoadingSpinner>
            </div>
            <div v-if="!isLoadingLatestScore && showTheseEntries.length === 0">
              <a href="#" v-on:click.prevent="showSection = sections.SCHEDULE"
                >Schedule</a
              >
              Nothing available.
            </div>
          </div>
        </div>
      </div>

      <div v-if="showTheseResultRows.length > 0">
        <ResultsPublicField
          v-if="showTheseResultRowsType !== 'T'"
          :event-name="showTheseEntries[0].eventGroup.groupName"
          :public-heat-group-meta="showThisMeta"
          :results="showTheseResultRows"
          v-on:close="showSection = sections.SCHEDULE"
        >
        </ResultsPublicField>
        <ResultsPublicTrack
          v-if="showTheseResultRowsType === 'T'"
          :event-name="showTheseEntries[0].eventGroup.groupName"
          :public-heat-group-meta="showThisMeta"
          :results="showTheseResultRows"
          v-on:close="showSection = sections.SCHEDULE"
        >
        </ResultsPublicTrack>
      </div>
    </div>

    <!--    <LoadingSpinnerModal message="Loading..." :show-it="isLoading" />-->
    <LoadingSpinnerV2 v-if="isLoading" />
  </div>
</template>

<script lang="ts">
import * as R from "ramda";
import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import { mapGetters, mapState } from "vuex";
import { Rs4Service } from "../../../scoreboard/rs4/rs4-service";
import { CommonService } from "../../../../common/common-service";
import { ScoreboardData } from "../../../scoreboard/scoreboard-data";
import {
  IPublicHeatGroupMeta,
  IR4sCompSchedule,
  IR4sEntry,
  IR4sScheduledEvent,
  IResultRow,
  IScheduleTableRow,
  R4SEventType,
} from "../../../scoreboard/rs4/rs4-scoreboard-models";
import { ISimpleDateModel } from "../../../../common/common-models";
import ResultsPublicTrack from "../../../scoreboard/rs4/output/public/results/results-public-track.vue";
import ResultsPublicField from "../../../scoreboard/rs4/output/public/results/results-public-field.vue";
import RankingsPublic from "../../../scoreboard/rs4/output/public/rankings/rankings-public.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "../../../../config/config-store";
import { handleResponseMessages } from "../../../../common/handle-http-reponse";
import { format, parse } from "date-fns";
import { CONFIG } from "../../../../common/config";
import { ConfigService } from "../../../../config/config-service";
import { IConfigApp } from "../../../../config/config-app-models";
import { IResultsStoreState, RESULTS_STORE_CONST } from "../../results-store";
import InputDebounce from "../../../../common/ui/field/input-debounce.vue";
import { RawLocation } from "vue-router";
import { LAUNCH_ROUTES_PATHS } from "../../../../launch/launch-routes";
import ResultEntries from "./result-entries.vue";
import { ResultsService } from "../../results-service";
import { ScoreboardService } from "../../../scoreboard/scoreboard-service";
import { CompEventData } from "../../../../compevent/compevent-data";
import ButtonGenericV2 from "../../../../common/ui/layoutV2/buttons/button-generic-v2.vue";
import ResultReadWrapper from "../read/result-read-wrapper.vue";
import LoadingSpinnerV2 from "../../../../common/ui/loading-spinner-v2.vue";
import {
  EventType,
  EventTypeNameGeneric,
} from "../../../../athleteCompSched/athletecompsched-models";
import FieldSelectV2 from "../../../../common/ui/layoutV2/fields/field-select-v2.vue";
import { simpleClone } from "../../../../common/common-service-utils";
import FormGenericInputTemplateV2 from "../../../../common/ui/layoutV2/form/form-generic-input-template-v2.vue";
import {
  filtersTrackFieldOptions,
  IScheduleCompPublicStoreState,
  SCHEDULE_COMP_PUBLIC_STORE_CONST,
} from "./schedule-comp-public-store";
import ButtonGenericBackV2 from "../../../../common/ui/layoutV2/buttons/button-generic-back-v2.vue";
import * as ResultsServiceV2 from "../../results-service-V2";
import {
  ISocketControllerInput,
  useSocketController,
} from "../../../../socket/useSocketController";
import { IShouldMessageBeDisplayedResult } from "../../../scoreboard/scoreboard-output/display/v3/scoreboard-output-display-service-v3";
import * as ScoreboardOutputDisplayServiceV3 from "../../../scoreboard/scoreboard-output/display/v3/scoreboard-output-display-service-v3";
import { IR4sSocketDataMessageDatav3 } from "../../../scoreboard/scoreboard-output/display/v3/useScoreboardOutputDisplayV3";
import {
  IScheduleCompPublicSocketSimpleInput,
  useScheduleCompPublicSocketSimple,
} from "./socket/useScheduleCompPublicSocketSimple";

const rs4Service: Rs4Service = new Rs4Service();

@Component({
  name: "schedule-comp-public",
  components: {
    ButtonGenericBackV2,
    FormGenericInputTemplateV2,
    FieldSelectV2,
    LoadingSpinnerV2,
    ResultReadWrapper,
    ButtonGenericV2,
    ResultEntries,
    InputDebounce,
    ResultsPublicTrack,
    ResultsPublicField,
    RankingsPublic,
  },
  computed: {
    ...mapGetters({
      isAdmin:
        CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME +
        "/" +
        CONFIG_STORE_CONST.CONFIG_GETTERS_IS_ADMIN,
    }),
    ...mapState(CONFIG_STORE_CONST.CONFIG_CONST_MODULE_NAME, {
      configApp: (state: any) => state.configApp,
    }),
    ...mapState(RESULTS_STORE_CONST.RESULTS_CONST_MODULE_NAME, {
      dateSelectedStore: (state: any) => state.dateSelected,
    }),
    ...mapState(
      SCHEDULE_COMP_PUBLIC_STORE_CONST.SCHEDULE_COMP_PUBLIC_MODULE_NAME,
      {
        scheduleCompPublicStoreState: (state: any) =>
          state,
      }
    ),
  },
})
export default class ScheduleCompPublic extends Vue {
  public readonly isAdmin!: boolean;
  public readonly configApp!: IConfigApp;
  public readonly dateSelectedStore!: ISimpleDateModel;
  public readonly scheduleCompPublicStoreState!: IScheduleCompPublicStoreState;

  @Prop({
    default: () => {
      return rs4Service.factoryR4sCompSchedule();
    },
  })
  public readonly r4sCompSchedule!: IR4sCompSchedule;

  @Prop({
    default: false,
  })
  public readonly useSimpleHeader!: boolean;

  public rs4Service: Rs4Service = rs4Service;
  public commonService: CommonService = new CommonService();
  public scoreboardData: ScoreboardData = new ScoreboardData();
  public configService: ConfigService = new ConfigService();
  public resultsService: ResultsService = new ResultsService();
  public scoreboardService: ScoreboardService = new ScoreboardService();

  public r4sCompScheduleTarget: IR4sCompSchedule =
    this.rs4Service.factoryR4sCompSchedule();

  public sections = {
    SCHEDULE: "SCHEDULE",
    RANKING: "RANKING",
    ENTRIES: "ENTRIES",
    RESULTS: "RESULTS",
  };
  public showSection: string = this.sections.SCHEDULE;

  public scheduleDates: ISimpleDateModel[] = [];
  public scheduleDateDisplay: ISimpleDateModel = {
    iso: "",
    display: "",
  };
  public scheduleTableRowDisplay: IScheduleTableRow =
    this.scoreboardService.factoryScheduleTableRow();

  public scheduleTableRows: IScheduleTableRow[] = [];
  public scheduleMap: Record<EventType, IR4sScheduledEvent[]> = {
    T: [],
    F: [],
    M: [],
    R: [],
    X: [],
  };

  public eventGroupIdKeyEntries: Record<string, IR4sEntry[]> = {};
  public eventGroupIdKeyMeta: Record<
    string,
    Record<number, IPublicHeatGroupMeta>
  > = {};
  public showTheseEntries: IR4sEntry[] = [];

  public showThisMeta: Record<number, IPublicHeatGroupMeta> =
    this.rs4Service.factoryPublicEventGroupMeta();
  public showTheseResultRows: IResultRow[] = [];
  public showTheseResultRowsType: R4SEventType | "" = "";
  public isLoadingLatestScore = false;
  public isLoading = false;

  public defaultFilterValue = "";
  // public filtersTrackFieldOptions: {
  //   id: EventTypeNameGeneric | "ALL";
  //   name: string;
  // }[] = [
  //   {
  //     id: "ALL",
  //     name: "All",
  //   },
  //   {
  //     id: "Track",
  //     name: "Track",
  //   },
  //   {
  //     id: "Field",
  //     name: "Field",
  //   },
  // ];
  public filtersTrackFieldOptions = filtersTrackFieldOptions;

  public filterTrackField = this.filtersTrackFieldOptions[0];

  public getNoResultsConfirmationFor: Record<string, boolean> = {};

  public eventGroupIdSelected: number = 0;

  public socketController: any;

  public scheduleCompPublicSocketSimple: any = null;

  public created() {
    const scheduleDates = this.getCompResultDates();
    const scheduleDatesIso = scheduleDates.map((scheduleDate) => {
      return scheduleDate.iso;
    });

    this.scheduleDates = scheduleDates;
    if (this.dateSelectedStore.iso.length > 0) {
      this.scheduleDateDisplay = this.dateSelectedStore;
    } else {
      const todayDate = format(new Date(), "YYYY-MM-DD");
      const datePosition = scheduleDatesIso.indexOf(todayDate);
      if (datePosition > -1) {
        this.scheduleDateDisplay = scheduleDates[datePosition];
      } else {
        this.scheduleDateDisplay = this.scheduleDates[0];
      }
    }

    this.createScheduleTableRows();

    const socketControllerInput: ISocketControllerInput = {
      startImmediately: true,
      onMessage: this.socketReceiveMessage,
    };
    this.socketController = useSocketController(socketControllerInput);

    const allEventGroupIdsFromScheduleRows = this.scheduleTableRows.map(
      (scheduleTableRow) => {
        return scheduleTableRow.eventGroupId;
      }
    );

    //  TODO change the "*" domain to the correct domain: window.location.origin
    const scheduleCompPublicSocketSimpleInput: IScheduleCompPublicSocketSimpleInput =
      {
        compIds: [this.r4sCompSchedule.compId, 548],
        domains: [window.location.origin],
        eventGroupIds: allEventGroupIdsFromScheduleRows,
        listenForMessageTypes: [
          "photofinish",
          "video",
          "confirmHeat",
          "message",
          "field-results",
        ],
      };

    this.scheduleCompPublicSocketSimple = useScheduleCompPublicSocketSimple(
      scheduleCompPublicSocketSimpleInput
    );
  }

  @Watch("r4sCompSchedule")
  public onR4sCompScheduleChanged() {
    this.createScheduleTableRows();
  }

  public socketReceiveMessage(message: any) {
    console.log("socket message", message);

    // const action = message.action;

    const data: IR4sSocketDataMessageDatav3 = JSON.parse(message.data);

    // console.log("data", data);

    const shouldMessageBeDisplayedResult: IShouldMessageBeDisplayedResult =
      this.scheduleCompPublicSocketSimple.shouldMessageBeDisplayed(data);

    const eventGroupID =
      ScoreboardOutputDisplayServiceV3.getEventIdFromMessage(data);
    // eventGroupID = 5922;
    // this.scheduleTableRows.forEach((scheduleTableRow) => {
    //   if (scheduleTableRow.eventGroupId === eventGroupID) {
    //     scheduleTableRow.hasResults = true;
    //   }
    // });

    if (shouldMessageBeDisplayedResult.should) {
      console.warn(
        "shouldMessageBeDisplayedResult eventGroupID: " + eventGroupID,
        shouldMessageBeDisplayedResult
      );
    } else {
      console.log(
        "shouldMessageBeDisplayedResult eventGroupID: " + eventGroupID,
        shouldMessageBeDisplayedResult
      );
    }

    if (shouldMessageBeDisplayedResult.should) {
      //  get event group id and set the results button enabled.
      this.scheduleTableRows.forEach((scheduleTableRow) => {
        if (scheduleTableRow.eventGroupId === eventGroupID) {
          scheduleTableRow.hasResults = true;
        }
      });

      //  Should we somehow reload...no, will crush the server.
      // this.getLatestScoreData(
      //   this.r4sCompSchedule.compId,
      //   shouldMessageBeDisplayedResult.eventGroupIds
      // );
    }
  }

  public reload() {
    this.$emit("reload");
  }

  public selectedScheduleByDate(startDate: ISimpleDateModel) {
    this.scheduleDateDisplay = startDate;
    this.showSection = this.sections.SCHEDULE;

    this.$store.commit(
      RESULTS_STORE_CONST.RESULTS_CONST_MODULE_NAME +
        "/" +
        RESULTS_STORE_CONST.RESULTS_MUTATIONS_SET_DATE,
      R.clone(startDate)
    );

    this.createScheduleTableRows();
  }

  public getCompResultDates(): ISimpleDateModel[] {
    return this.commonService.sortArray(
      "iso",
      this.commonService.uniqueArrayById(
        this.r4sCompSchedule.schedule.map((schedule) => {
          return {
            iso: schedule.startDate.split("T")[0],
            display: format(parse(schedule.startDate), "Do MMM"),
          };
        }),
        "iso"
      )
    );
  }

  public isDateSelected(simpleDateModel: ISimpleDateModel): boolean {
    return simpleDateModel.iso === this.scheduleDateDisplay.iso;
  }

  public sortEvents(
    r4sScheduledEvents: IR4sScheduledEvent[]
  ): IR4sScheduledEvent[] {
    return this.commonService.sortArray("startDate", r4sScheduledEvents);
  }

  public createScheduleTableRows(): void {
    this.scheduleMap = this.rs4Service.sortScheduleMap(
      this.r4sCompSchedule.schedule
    );

    const filterTrackField = this.scheduleCompPublicStoreState.filterTrackField;

    let eventTypes: EventType[] = ["T", "F", "M", "R", "X"];
    if (filterTrackField.id === "Track") {
      eventTypes = ["T", "R", "X"];
    }
    if (filterTrackField.id === "Field") {
      eventTypes = ["F"];
    }
    const scheduledEvents: IR4sScheduledEvent[] = eventTypes.reduce<
      IR4sScheduledEvent[]
    >((accum, key) => {
      const toAdd = simpleClone(this.scheduleMap[key]);
      accum = accum.concat(toAdd);
      return accum;
    }, []);

    let scheduleRows: IScheduleTableRow[] = scheduledEvents
      .map((scheduledEvent) => {
        return this.rs4Service.mapScheduleEventToScheduleTableRow(
          scheduledEvent
        );
      })
      .filter((scheduleTableRow) => {
        return (
          scheduleTableRow.startTime.split("T")[0] ===
          this.scheduleDateDisplay.iso
        );
      });

    const defaultFilterValue =
      this.scheduleCompPublicStoreState.defaultFilterValue;

    if (defaultFilterValue.length > 0) {
      const searchTerm = defaultFilterValue.toLowerCase();
      scheduleRows = scheduleRows.filter((scheduleTableRow) => {
        return (
          scheduleTableRow.eventName.toLowerCase().indexOf(searchTerm) > -1
        );
      });
    }
    // const trackRows: IScheduleTableRow[] = Object.keys(this.trackPayloads).map( (key) => {
    //     return this.rs4Service.mapTrackToScheduleTableRow(this.trackPayloads[key]);
    // })

    this.scheduleTableRows = this.commonService.sortArray(
      "startTime",
      scheduleRows
    );

    this.getNoResultsConfirmationFor = this.scheduleTableRows.reduce<
      Record<string, boolean>
    >((accum, row) => {
      accum[row.eventGroupId.toString()] = false;
      return accum;
    }, {});
  }

  public get hasResultsPermissionForComp() {
    return this.configService.hasResultsPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public get getLogoPath() {
    return CONFIG.E4S_HOST + this.r4sCompSchedule.logo;
  }

  public get getDate() {
    return format(parse(this.r4sCompSchedule.date), "Do MMM YYYY");
  }

  public getEventTime(r4sScheduledEvent: IR4sScheduledEvent) {
    return format(parse(r4sScheduledEvent.startDate), "HH:mm");
  }

  public getEventTimeForRow(scheduleTableRow: IScheduleTableRow) {
    const time = format(parse(scheduleTableRow.startTime), "HH:mm");
    return time === "00:00" ? "TBC" : time;
  }

  public hasResultsWaitingText(scheduleTableRow: IScheduleTableRow): string {
    if (!scheduleTableRow.resultsPossible) {
      return "";
    }
    if (scheduleTableRow.hasResults) {
      return scheduleTableRow.waiting ? " ( Awaiting seeding )" : "";
    }
    return "(Pending)";
  }

  public getLatestScoreData(
    compId: number,
    eventGroupIds: number[]
  ): Promise<boolean> {
    if (compId === 0 || eventGroupIds.length === 0) {
      return Promise.resolve(false);
    }
    this.isLoadingLatestScore = true;

    const prom = this.scoreboardData.getLatestScores(compId, eventGroupIds);
    handleResponseMessages(prom);
    return prom
      .then((resp) => {
        if (resp.errNo > 0) {
          return false;
        }
        this.addEntriesByEventIds(resp.data);
        if (resp.meta) {
          this.addMetaByEventIds(resp.meta);
        }
        return true;
      })
      .finally(() => {
        this.isLoadingLatestScore = false;
      });
  }

  public addEntriesByAthlete(entries: Record<number, IR4sEntry>): void {
    if (entries && Object.keys(entries).length > 0) {
      const entriesArray = this.commonService.convertObjectToArray(entries);
      if (entriesArray.length === 0) {
        return;
      }
      const eventGroupId: string = entriesArray[0].eventGroup.id.toString();
      this.eventGroupIdKeyEntries[eventGroupId] = entriesArray;
    }
  }

  public addEntriesByEventIds(
    entriesByEventIds: Record<number, Record<number, IR4sEntry>>
  ) {
    Object.values(entriesByEventIds).forEach((entriesByAthleteId) => {
      this.addEntriesByAthlete(entriesByAthleteId);
    });
  }

  public addMetaByEventIds(
    metasByEventKeyMap: Record<number, Record<number, IPublicHeatGroupMeta>>
  ) {
    const keys = Object.keys(metasByEventKeyMap);
    //  @ts-ignore
    keys.forEach((eventId: number) => {
      const key = eventId.toString();
      this.eventGroupIdKeyMeta[key] = metasByEventKeyMap[eventId];
    });
  }

  public displayRankings(eventGroupId: number) {
    this.showTheseEntries = [];
    this.showTheseResultRows = [];
    //  Get from the cache.  Any "new" scores will come up the socket and get loaded to cache.
    this.showSection = this.sections.RANKING;
    // if (this.eventGroupIdKeyEntries[eventGroupId.toString()]) {
    //     this.getFromCache(eventGroupId);
    //     return;
    // }
    this.getLatestScoreData(this.r4sCompSchedule.compId, [eventGroupId]).then(
      () => {
        this.getFromCache(eventGroupId);
      }
    );
  }

  public getFromCache(eventGroupId: number) {
    const eventType: R4SEventType = this.r4sCompSchedule.schedule.reduce(
      (accum, schedule) => {
        if (schedule.id.toString() === eventGroupId.toString()) {
          accum = schedule.type;
        }
        return accum;
      },
      "T" as R4SEventType
    );

    // const includeZeroAndDnsDnf = true;
    // const r4sEntries = this.rs4Service.sortAndFilterEntries(
    //     R.clone(this.eventGroupIdKeyEntries[eventGroupId.toString()]),
    //     eventType,
    //     includeZeroAndDnsDnf
    // );
    const r4sEntries = this.rs4Service.sortAndFilterResultEntries(
      R.clone(this.eventGroupIdKeyEntries[eventGroupId.toString()]),
      eventType
    );
    this.showTheseEntries = r4sEntries;

    if (eventType === "T") {
      this.showTheseResultRows =
        this.rs4Service.mapR4sEntriesToResultRowTracks(r4sEntries);
      // this.showTheseResultRows = r4sEntries.map( (entry) => {
      //     return this.rs4Service.mapR4sEntryToResultRowTrack(entry);
      // });
    } else {
      this.showTheseResultRows = r4sEntries.map((entry) => {
        return this.rs4Service.mapR4sEntryToIResultRow(entry);
      });
    }
    this.showThisMeta = this.rs4Service.factoryPublicEventGroupMeta();
    if (this.eventGroupIdKeyMeta[eventGroupId.toString()]) {
      this.showThisMeta = R.clone(this.eventGroupIdKeyMeta[eventGroupId]);
    }
    this.showTheseResultRowsType = eventType;
  }

  public exportToPof10() {
    window.open(
      CONFIG.E4S_HOST + "/" + this.r4sCompSchedule.compId + "/pof10",
      "Pof10-export"
    );
  }

  public goToScheduleQr() {
    let location: RawLocation;
    location = {
      path:
        "/" +
        LAUNCH_ROUTES_PATHS.R4S_RESULTS_PUBLIC_QR +
        "/" +
        this.r4sCompSchedule.compId,
    };
    this.$router.push(location);
  }

  public doFilter(searchTerm: string) {
    this.defaultFilterValue = searchTerm;
    this.$store.commit(
      SCHEDULE_COMP_PUBLIC_STORE_CONST.SCHEDULE_COMP_PUBLIC_MODULE_NAME +
        "/" +
        SCHEDULE_COMP_PUBLIC_STORE_CONST.SCHEDULE_COMP_PUBLIC__MUTATIONS__QUICK_FILTER,
      searchTerm
    );
    this.createScheduleTableRows();
  }

  public showEntries(eventGroupId: number) {
    const scheduleTableRow: IScheduleTableRow | null =
      this.scheduleTableRows.reduce<IScheduleTableRow | null>((accum, row) => {
        if (row.eventGroupId === eventGroupId) {
          accum = row;
        }
        return accum;
      }, null);

    if (this.getIsThisAFeederComp && this.r4sCompScheduleTarget.compId === 0) {
      //  TODO, check the "true"...massive perf hit.
      const prom = this.scoreboardData.getCompSchedule(
        this.r4sCompSchedule.autoEntries.selectedTargetComp.id,
        true
      );
      handleResponseMessages(prom);
      prom.then((resp) => {
        if (resp.errNo === 0) {
          this.r4sCompScheduleTarget = resp.data;
        }
      });
    }

    if (scheduleTableRow) {
      this.isLoading = true;
      this.scoreboardData
        .getEntriesForEventGroup(
          this.r4sCompSchedule.compId,
          scheduleTableRow.eventGroupId
        )
        .then((response) => {
          if (response.errNo === 0) {
            const scheduleTableRowDisplay = R.clone(scheduleTableRow);
            scheduleTableRowDisplay.athleteEntries = response.data;
            this.scheduleTableRowDisplay = scheduleTableRowDisplay;
            this.showSection = this.sections.ENTRIES;
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    }
  }

  public get getIsThisAFeederComp() {
    return this.rs4Service.isFeederComp(this.r4sCompSchedule);
  }

  public goToListFormat() {
    window.open(
      CONFIG.E4S_HOST + "/" + this.r4sCompSchedule.compId + "/resultslist",
      "_e4slistformat"
    );
  }

  public doGetNoResultsConfirmation(
    scheduleTableRow: IScheduleTableRow,
    getConfirm: boolean
  ) {
    this.getNoResultsConfirmationFor[scheduleTableRow.eventGroupId] =
      getConfirm;
  }

  public doSetNoResults(
    scheduleTableRow: IScheduleTableRow,
    isPossible: boolean
  ) {
    this.isLoading = true;
    const prom = new CompEventData().setNoResultsPossible(
      scheduleTableRow.eventGroupId,
      isPossible
    );
    handleResponseMessages(prom);
    prom
      .then((resp) => {
        if (resp.errNo === 0) {
          this.reload();
        }
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  public get getHasBuilderPermissionForComp(): boolean {
    return this.configService.hasBuilderPermissionForComp(
      this.configApp.userInfo,
      this.r4sCompSchedule.org.id,
      this.r4sCompSchedule.compId
    );
  }

  public showResults(eventGroupId: number) {
    this.showSection = "RESULTS";
    this.eventGroupIdSelected = eventGroupId;
  }

  public gotoNextResult(goForward: boolean) {
    let tableRow: IScheduleTableRow | null = null;
    const currentlySelectedPostition: number =
      this.scheduleTableRows.reduce<number>((accum, tableRow, index) => {
        if (this.eventGroupIdSelected === tableRow.eventGroupId) {
          accum = index;
        }
        return accum;
      }, 0);
    const currentLength = this.scheduleTableRows.length;

    const nextPosition = goForward
      ? currentlySelectedPostition + 1
      : currentlySelectedPostition - 1;
    if (goForward) {
      if (nextPosition >= currentLength) {
        //  at end, go back to beginning
        tableRow = this.scheduleTableRows[0];
      } else {
        tableRow = this.scheduleTableRows[nextPosition];
      }
    } else {
      if (nextPosition < 0) {
        //  at beginning, go back to end
        tableRow = this.scheduleTableRows[currentLength - 1];
      } else {
        tableRow = this.scheduleTableRows[nextPosition];
      }
    }

    this.eventGroupIdSelected = tableRow
      ? tableRow.eventGroupId
      : this.scheduleTableRows[0].eventGroupId;
  }

  public filterTrackFieldSelected(trackField: {
    id: EventTypeNameGeneric | "ALL";
    name: string;
  }) {
    this.filterTrackField = trackField;
    this.$store.commit(
      SCHEDULE_COMP_PUBLIC_STORE_CONST.SCHEDULE_COMP_PUBLIC_MODULE_NAME +
        "/" +
        SCHEDULE_COMP_PUBLIC_STORE_CONST.SCHEDULE_COMP_PUBLIC__MUTATIONS__FILTER_TRACK_FIELD,
      trackField
    );
    this.createScheduleTableRows();
  }

  public get getHasCompAnyResults() {
    return ResultsServiceV2.hasAnyResults(this.r4sCompSchedule.schedule);
  }

  public reloadSchedule() {
    console.log("ScheduleCompPublic.reloadSchedule reloadSchedule");
    this.$emit("reloadSchedule");
  }
}
</script>

<style scoped>
.r4s-schedule-public--header {
  border-bottom: 1px solid gray;
  padding: 0.5em 0;
}

.r4s-schedule-public--comp-name {
  font-size: 1.5em;
}

.r4s-schedule-public--comp-date-section {
  margin-top: 1em;
}

.r4s-schedule-public--comp-date-link {
  padding: 0 0.5em;
  border-right: 1px solid #c5c5c5;
}

.r4s-schedule-public--comp-date-link-selected {
  color: black;
}

.r4s-schedule-public--logo {
  height: 4em;
}

.r4s-schedule-public--schedule-row-head {
  margin: 0.5em 0;
}

.r4s-schedule-public--schedule-row-head-label {
  font-weight: 600;
}

.r4s-schedule-public--schedule-row {
  border-top: 1px solid lightgrey;
  //border-bottom: 1px solid lightgrey;
  padding: 0.5em 0;
}

.r4s-schedule-public--schedule-row-odd {
  background-color: #f7f7f6;
}

.r4s-schedule-public--ranking-header {
  margin: 0.5em 0;
  font-size: 1.25em;
}

.r4s-schedule-public--pending-results {
  color: darkgray;
}

.schedule-comp-public--row-cancel {
  background-color: var(--slate-100);
  color: var(--slate-400);
}

.schedule-comp-public--slim-row-button {
  width: 80px;
}
</style>
