<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Entry4Sports</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

    <!-- Third-party CSS/JS still loaded via CDN as in webpack index -->
    <link
      rel="stylesheet"
      href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/themes/base/jquery-ui.css"
    />

    <!-- NOTE: App CSS is imported inside src/index.ts and bundled by Vite -->

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js"></script>
    <!-- Load Materialize JS in global (non-module) context so window.M is available -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js" integrity="sha512-Cu3Acp0bTQAYQ5Qa0Pn3NbS1Ry6j8TDIrS9KuX3sI5Yucs5cjox96D65gis6pZeRAgvIJ5zsxFr2iGaI6hV7NQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  </head>
  <body>

    <div id="app">
      <router-view :key="$route.path"></router-view>
    </div>

    <script type="module" src="/src/index.ts"></script>
    <script>
      var E4S_GLOBAL_THEME_IDENTIFIER = "def";
    </script>
  </body>
</html>